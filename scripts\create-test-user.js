const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createTestUser() {
  try {
    console.log('创建测试用户...')
    
    // 检查用户是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })
    
    if (existingUser) {
      console.log('用户已存在:', existingUser.email)
      return existingUser
    }
    
    // 创建密码哈希
    const hashedPassword = await bcrypt.hash('123456', 10)
    
    // 创建用户
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'testuser',
        password: hashedPassword,
        score: 0,
        level: 1
      }
    })
    
    console.log('用户创建成功:', {
      id: user.id,
      email: user.email,
      username: user.username
    })
    
    return user
    
  } catch (error) {
    console.error('创建用户失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createTestUser()
