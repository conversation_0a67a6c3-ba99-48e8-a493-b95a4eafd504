'use client'

import { useSession } from 'next-auth/react'
import { useEffect, useState, useCallback } from 'react'
import { useRouter, useParams } from 'next/navigation'
import Link from 'next/link'
import dynamic from 'next/dynamic'
import { validateTask } from '@/app/lib/validation'
import { UniverInstance, UniverAPI } from '@/types/univer'

// 动态导入UniverWrapper组件
const UniverWrapper = dynamic(() => import('@/app/components/UniverWrapper'), {
  ssr: false,
  loading: () => (
    <div className="flex flex-col items-center justify-center h-96 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
      <div className="text-lg font-medium text-gray-700 mb-2">表格加载中，请稍后……</div>
      <div className="text-sm text-gray-500">正在初始化Excel组件</div>
    </div>
  )
})

interface Task {
  id: string
  name: string
  description: string
  type: string
  order: number
  validation: string
  initialData?: string
}

interface Level {
  id: string
  name: string
  description: string
  difficulty: number
  points: number
  parentId?: string
  tasks: Task[]
  progress: Array<{
    completed: boolean
    score: number
    attempts: number
  }>
}

export default function TaskPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const params = useParams()
  const taskId = params.id as string
  
  const [level, setLevel] = useState<Level | null>(null)
  const [currentTask, setCurrentTask] = useState<Task | null>(null)
  const [univerInstance, setUniverInstance] = useState<UniverInstance | null>(null)
  const [univerAPI, setUniverAPI] = useState<UniverAPI | null>(null)
  const [loading, setLoading] = useState(true)
  const [message, setMessage] = useState('')
  const [submitting, setSubmitting] = useState(false)
  const [taskInitialData, setTaskInitialData] = useState<Record<string, unknown>>({})

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
    }
  }, [status, router])

  const fetchTask = useCallback(async () => {
    try {
      const response = await fetch(`/api/tasks/${taskId}`)
      if (response.ok) {
        const taskData = await response.json()
        setCurrentTask(taskData.task)
        setLevel(taskData.level)
        
        // 设置初始数据
        if (taskData.task.initialData) {
          try {
            const initialData = JSON.parse(taskData.task.initialData)
            setTaskInitialData(initialData)
          } catch (error) {
            console.error('解析初始数据失败:', error)
            setTaskInitialData({})
          }
        }
      } else {
        router.push('/dashboard')
      }
    } catch (error) {
      console.error('获取任务失败:', error)
      router.push('/dashboard')
    } finally {
      setLoading(false)
    }
  }, [taskId, router])

  useEffect(() => {
    if (session && taskId) {
      fetchTask()
    }
  }, [session, taskId, fetchTask])

  const validateCurrentTask = async (task: Task) => {
    if (!univerAPI) {
      setMessage('Excel组件未加载完成，请稍后重试')
      return false
    }

    try {
      const validationRule = JSON.parse(task.validation)
      const result = await validateTask(univerAPI, validationRule)
      
      if (!result.success) {
        setMessage(result.message)
      }
      
      return result.success
    } catch (error) {
      console.error('验证任务失败:', error)
      setMessage('验证过程中发生错误，请检查任务配置')
      return false
    }
  }

  // 处理Univer实例变化 - 使用useCallback稳定引用
  const handleUniverReady = useCallback((instance: UniverInstance, api: UniverAPI) => {
    setUniverInstance(instance)
    setUniverAPI(api)
    console.log('Univer实例已准备就绪')
  }, [])

  const handleTaskSubmit = async () => {
    if (!currentTask) return
    
    setSubmitting(true)
    setMessage('')
    
    const isValid = await validateCurrentTask(currentTask)
    
    if (isValid) {
      try {
        // 提交任务完成状态
        const response = await fetch('/api/progress', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            levelId: level?.id,
            completed: true,
            score: level?.points || 0
          })
        })
        
        if (response.ok) {
          setMessage('任务完成！3秒后返回关卡列表...')
          setTimeout(() => {
            // 跳转到父级关卡（主任务）页面
            // 如果当前关卡有parentId，说明它是子关卡，应该跳转到父级主任务
            // 如果没有parentId，说明它本身就是主任务
            // const targetLevelId = level?.parentId || level?.id
            if (level?.parentId) {
              // 子关卡完成后跳转到父级主任务页面
              router.push(`/level/${level.parentId}`)
            } else {
              // 主任务完成后跳转到dashboard
              router.push('/dashboard')
            }
          }, 3000)
        } else {
          setMessage('提交失败，请重试')
        }
      } catch (error) {
        console.error('提交任务失败:', error)
        setMessage('提交失败，请重试')
      }
    } else {
      setMessage('任务未完成，请检查你的操作是否正确')
    }
    
    setSubmitting(false)
  }

  const resetTask = () => {
    setMessage('')
    // 重置Excel表格
    if (univerInstance) {
      // 这里需要实现重置表格的逻辑
      console.log('重置表格')
    }
  }

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (!session || !currentTask || !level) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link
                href={level.parentId ? `/level/${level.parentId}` : '/dashboard'}
                className="text-green-600 hover:text-green-800"
              >
                ← 返回任务列表
              </Link>
              <h1 className="text-xl font-semibold text-gray-900">
                {currentTask.name}
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-700">
                任务类型: {currentTask.type}
              </span>
              <button
                onClick={resetTask}
                className="text-sm text-gray-600 hover:text-gray-800"
              >
                重置
              </button>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-16xl mx-auto py-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 任务说明面板 */}
          <div className="lg:col-span-1">
            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">
                任务详情
              </h2>
              
              <div>
                <h3 className="text-md font-medium text-gray-800 mb-2">
                  {currentTask.name}
                </h3>
                <div className="text-sm text-gray-600 mb-4">
                  {currentTask.description.split('\n').map((line, index) => {
                    // 处理操作步骤
                    if (line.trim().match(/^\d+\./)) {
                      return (
                        <div key={index} className="flex items-start mb-2 p-2 bg-blue-50 rounded border-l-4 border-blue-400">
                          <span className="flex w-6 h-6 bg-blue-500 text-white text-xs rounded-full items-center justify-center mr-2 mt-0.5 flex-shrink-0">
                            {line.trim().match(/^(\d+)\./)?.[1]}
                          </span>
                          <span className="text-gray-700">{line.replace(/^\d+\.\s*/, '')}</span>
                        </div>
                      )
                    }
                    // 处理标题行
                    else if (line.includes('操作步骤：') || line.includes('任务说明：') || line.includes('完成后')) {
                      return (
                        <div key={index} className="font-medium text-gray-800 mt-3 mb-2">
                          {line}
                        </div>
                      )
                    }
                    // 处理提示信息
                    else if (line.includes('提示：')) {
                      return (
                        <div key={index} className="mt-3 p-2 bg-yellow-50 border-l-4 border-yellow-400 text-sm text-gray-700">
                          <span className="font-medium text-yellow-800">💡 </span>
                          {line.replace('提示：', '')}
                        </div>
                      )
                    }
                    // 处理普通文本行
                    else if (line.trim()) {
                      return (
                        <div key={index} className="mb-1">
                          {line}
                        </div>
                      )
                    }
                    return null
                  })}
                </div>
                
                <div className="space-y-2 mb-4">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">任务类型:</span>
                    <span className="font-medium">{currentTask.type}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">关卡积分:</span>
                    <span className="font-medium">{level.points}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">难度:</span>
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <span
                          key={i}
                          className={`text-sm ${
                            i < level.difficulty ? 'text-yellow-400' : 'text-gray-300'
                          }`}
                        >
                          ★
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
                
                {/* 任务完成/未完成提示 - 在提交按钮上方 */}
                {message && (
                  <div className={`text-base px-4 py-2 rounded-lg font-medium mb-4 ${
                    message.includes('任务完成') || message.includes('恭喜') || message.includes('通过') || message.includes('成功')
                      ? 'bg-green-100 text-green-800 border border-green-200'
                      : 'bg-red-100 text-red-800 border border-red-200'
                  }`}>
                    {message}
                  </div>
                )}
                
                <div className="space-y-2">
                  <button
                    onClick={handleTaskSubmit}
                    disabled={submitting}
                    className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:opacity-50"
                  >
                    {submitting ? '验证中...' : '提交任务'}
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Excel练习区域 */}
          <div className="lg:col-span-2">
            <div className="bg-white shadow rounded-lg p-3">
              <div className="mb-3">
                <h2 className="text-lg font-medium text-gray-900">
                  Excel练习区
                </h2>
              </div>
              
              <div className="border rounded-lg overflow-hidden">
                <UniverWrapper 
                  onReady={handleUniverReady} 
                  initialData={taskInitialData}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}