const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function debugTask() {
  try {
    console.log('=== 调试数据输入任务 ===')
    
    // 查找数据输入任务
    const task = await prisma.task.findFirst({
      where: {
        name: '数据输入'
      },
      include: {
        level: true
      }
    })
    
    if (!task) {
      console.log('未找到数据输入任务')
      return
    }
    
    console.log('任务信息:')
    console.log('- ID:', task.id)
    console.log('- 名称:', task.name)
    console.log('- 类型:', task.type)
    console.log('- 关卡:', task.level.name)
    console.log('- 验证规则:', task.validation)
    
    try {
      const validation = JSON.parse(task.validation)
      console.log('\n解析后的验证规则:')
      console.log('- 类型:', validation.type)
      console.log('- 单元格:', validation.cell)
      console.log('- 期望值:', validation.expectedValue)
      
      // 检查验证规则是否正确
      if (validation.type === 'cellValue' && validation.cell === 'A1' && validation.expectedValue === 'Hello Excel') {
        console.log('✓ 验证规则配置正确')
      } else {
        console.log('✗ 验证规则配置有问题')
      }
    } catch (error) {
      console.log('✗ 验证规则JSON解析失败:', error.message)
    }
    
    // 检查初始数据
    if (task.initialData) {
      try {
        const initialData = JSON.parse(task.initialData)
        console.log('\n初始数据:', initialData)
      } catch (error) {
        console.log('\n初始数据解析失败:', error.message)
      }
    } else {
      console.log('\n无初始数据')
    }
    
    console.log('\n=== 调试完成 ===')
    
  } catch (error) {
    console.error('调试过程中出错:', error)
  } finally {
    await prisma.$disconnect()
  }
}

debugTask()
