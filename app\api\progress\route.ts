import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/app/lib/auth'
import { prisma } from '@/app/lib/db'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      )
    }

    const { levelId, completed, score } = await request.json()

    if (!levelId) {
      return NextResponse.json(
        { error: '关卡ID是必需的' },
        { status: 400 }
      )
    }

    // 验证用户是否存在
    const user = await prisma.user.findUnique({
      where: { id: session.user.id }
    })

    if (!user) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    // 获取关卡信息
    const level = await prisma.level.findUnique({
      where: { id: levelId }
    })

    if (!level) {
      return NextResponse.json(
        { error: '关卡不存在' },
        { status: 404 }
      )
    }

    // 更新或创建用户进度
    const progress = await prisma.userProgress.upsert({
      where: {
        userId_levelId: {
          userId: session.user.id,
          levelId: levelId
        }
      },
      update: {
        completed: completed || false,
        score: score || 0,
        attempts: {
          increment: 1
        },
        completedAt: completed ? new Date() : null
      },
      create: {
        userId: session.user.id,
        levelId: levelId,
        completed: completed || false,
        score: score || 0,
        attempts: 1,
        completedAt: completed ? new Date() : null
      }
    })

    // 如果完成了关卡，更新用户总积分
    if (completed) {
      await prisma.user.update({
        where: { id: session.user.id },
        data: {
          score: {
            increment: level.points
          }
        }
      })
    }

    return NextResponse.json(progress)
  } catch (error) {
    console.error('更新进度错误:', error)
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      )
    }

    const progress = await prisma.userProgress.findMany({
      where: {
        userId: session.user.id
      },
      include: {
        level: true
      },
      orderBy: {
        level: {
          order: 'asc'
        }
      }
    })

    return NextResponse.json(progress)
  } catch (error) {
    console.error('获取进度错误:', error)
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}