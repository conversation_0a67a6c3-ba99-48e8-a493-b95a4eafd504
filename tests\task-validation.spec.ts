import { test, expect } from '@playwright/test'

// 测试配置
const TEST_USER = {
  email: '<EMAIL>',
  password: '123456'
}

const BASE_URL = 'http://localhost:3000'

test.describe('任务验证修复测试', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到首页
    await page.goto(BASE_URL)
    
    // 等待页面加载
    await page.waitForLoadState('networkidle')
  })

  test('数据输入任务 - 正确操作应该成功', async ({ page }) => {
    // 导航到数据输入任务
    await page.goto(`${BASE_URL}/task/cmcrdj5eg0004u4ogr9w3fd9a`)
    await page.waitForLoadState('networkidle')
    
    // 等待Excel组件加载
    await page.waitForSelector('[data-testid="excel-container"]', { timeout: 10000 })
    
    // 导航到A1单元格
    await page.keyboard.press('Control+Home')
    await page.keyboard.press('ArrowUp') // 确保在A1
    
    // 输入"Hello Excel"
    await page.keyboard.type('Hello Excel')
    await page.keyboard.press('Enter')
    
    // 提交任务
    await page.click('button:has-text("提交任务")')
    
    // 验证成功消息
    await expect(page.locator('text=任务完成')).toBeVisible({ timeout: 5000 })
  })

  test('数据输入任务 - 错误操作应该失败', async ({ page }) => {
    // 导航到数据输入任务
    await page.goto(`${BASE_URL}/task/cmcrdj5eg0004u4ogr9w3fd9a`)
    await page.waitForLoadState('networkidle')
    
    // 等待Excel组件加载
    await page.waitForSelector('[data-testid="excel-container"]', { timeout: 10000 })
    
    // 不输入任何内容，直接提交
    await page.click('button:has-text("提交任务")')
    
    // 验证失败消息
    await expect(page.locator('text=任务未完成')).toBeVisible({ timeout: 5000 })
  })

  test('数字格式任务 - 应该需要实际操作', async ({ page }) => {
    // 导航到数字格式任务
    await page.goto(`${BASE_URL}/task/cmcrdj5gj000mu4ogwati4ry6`)
    await page.waitForLoadState('networkidle')
    
    // 等待Excel组件加载
    await page.waitForSelector('[data-testid="excel-container"]', { timeout: 10000 })
    
    // 不进行任何格式设置，直接提交
    await page.click('button:has-text("提交任务")')
    
    // 验证失败消息（现在应该要求实际操作）
    await expect(page.locator('text=任务未完成')).toBeVisible({ timeout: 5000 })
  })

  test('柱状图任务 - 应该需要实际操作', async ({ page }) => {
    // 导航到柱状图任务
    await page.goto(`${BASE_URL}/task/cmcrdj5j1001bu4ogqxzcx4qj`)
    await page.waitForLoadState('networkidle')
    
    // 等待Excel组件加载
    await page.waitForSelector('[data-testid="excel-container"]', { timeout: 10000 })
    
    // 不创建图表，直接提交
    await page.click('button:has-text("提交任务")')
    
    // 验证失败消息（现在应该要求实际操作）
    await expect(page.locator('text=任务未完成')).toBeVisible({ timeout: 5000 })
  })

  test('单列排序任务 - 正确操作应该成功', async ({ page }) => {
    // 导航到单列排序任务
    await page.goto(`${BASE_URL}/task/cmcrdj5l9001yu4og30p2m6u3`)
    await page.waitForLoadState('networkidle')
    
    // 等待Excel组件加载
    await page.waitForSelector('[data-testid="excel-container"]', { timeout: 10000 })
    
    // 模拟排序操作（这里需要根据实际的Univer UI进行调整）
    // 1. 选择数据范围
    await page.keyboard.press('Control+Home')
    await page.keyboard.press('Control+Shift+End')
    
    // 2. 进行排序（这里可能需要更具体的操作步骤）
    // 由于Univer的排序操作比较复杂，我们先测试验证逻辑是否更宽松了
    
    // 提交任务
    await page.click('button:has-text("提交任务")')
    
    // 检查是否有更友好的错误消息
    const errorMessage = await page.locator('text=任务未完成').textContent()
    
    // 验证错误消息包含操作指导
    if (errorMessage) {
      expect(errorMessage).toContain('排序')
    }
  })

  test('多列排序任务 - 正确操作应该成功', async ({ page }) => {
    // 导航到多列排序任务
    await page.goto(`${BASE_URL}/task/cmcrdj5lm0022u4ogz3g9z7ow`)
    await page.waitForLoadState('networkidle')
    
    // 等待Excel组件加载
    await page.waitForSelector('[data-testid="excel-container"]', { timeout: 10000 })
    
    // 模拟多列排序操作
    await page.keyboard.press('Control+Home')
    await page.keyboard.press('Control+Shift+End')
    
    // 提交任务
    await page.click('button:has-text("提交任务")')
    
    // 检查是否有更友好的错误消息
    const errorMessage = await page.locator('text=任务未完成').textContent()
    
    // 验证错误消息包含操作指导
    if (errorMessage) {
      expect(errorMessage).toContain('多列排序')
    }
  })
})

test.describe('验证逻辑一致性测试', () => {
  test('所有任务都应该有合理的验证逻辑', async ({ page }) => {
    const problematicTasks = [
      { name: '数字格式', id: 'cmcrdj5gj000mu4ogwati4ry6', shouldRequireAction: true },
      { name: '柱状图', id: 'cmcrdj5j1001bu4ogqxzcx4qj', shouldRequireAction: true },
      { name: '单列排序', id: 'cmcrdj5l9001yu4og30p2m6u3', shouldRequireAction: true },
      { name: '多列排序', id: 'cmcrdj5lm0022u4ogz3g9z7ow', shouldRequireAction: true }
    ]

    for (const task of problematicTasks) {
      console.log(`测试任务: ${task.name}`)
      
      // 导航到任务页面
      await page.goto(`${BASE_URL}/task/${task.id}`)
      await page.waitForLoadState('networkidle')
      
      // 等待Excel组件加载
      await page.waitForSelector('[data-testid="excel-container"]', { timeout: 10000 })
      
      // 不进行任何操作，直接提交
      await page.click('button:has-text("提交任务")')
      
      if (task.shouldRequireAction) {
        // 应该显示失败消息
        await expect(page.locator('text=任务未完成')).toBeVisible({ timeout: 5000 })
        console.log(`✓ ${task.name}: 正确要求用户操作`)
      } else {
        // 应该显示成功消息
        await expect(page.locator('text=任务完成')).toBeVisible({ timeout: 5000 })
        console.log(`✓ ${task.name}: 正确允许无操作完成`)
      }
    }
  })
})
